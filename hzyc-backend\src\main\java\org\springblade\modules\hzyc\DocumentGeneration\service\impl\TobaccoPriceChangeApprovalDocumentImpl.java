package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 烟草专卖品变价处理审批表文档生成实现类
 *
 * <AUTHOR>
 */
@Service("tobaccoPriceChangeApprovalDocument")
public class TobaccoPriceChangeApprovalDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "烟草专卖品变价处理审批表.docx";
    }

    @Override
    public String getDocumentType() {
        return "TOBACCO-PRICE-CHANGE-APPROVAL";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("FWZXSJSCBJ", "sysisdelete");
        mapping.put("XYWYBS", "tid");
        mapping.put("ZZMC", "org_name");
        mapping.put("ZJE", "total_amount");
        mapping.put("WCZT", "complete_status");
        mapping.put("SJSSBM", "own_dept_uuid");
        mapping.put("KZZD1", "ext1");
        mapping.put("DW", "unit");
        mapping.put("AJBS", "case_uuid");
        mapping.put("WPFL", "goods_category");
        mapping.put("XTGXSJ", "sys_modify_time");
        mapping.put("CJSJ", "create_time");
        mapping.put("DSR", "party");
        mapping.put("ZS", "total_quantity");
        mapping.put("SJBM", "city_org_code");
        mapping.put("SJMC", "city_org_name");
        mapping.put("PFDJ", "wholesale_price");
        mapping.put("JBRUUIDS", "handler_uuids");
        mapping.put("MCRKSJ", "mc_tec_ctime");
        mapping.put("LYSHSL", "sample_loss_quantity");
        mapping.put("LSDJ", "retail_price");
        mapping.put("SL", "quantity");
        mapping.put("KZZD2", "ext2");
        mapping.put("SJSSDW", "own_org_uuid");
        mapping.put("PZ", "variety");
        mapping.put("WSRQ", "doc_date");
        mapping.put("WPMXBS", "goods_detail_id");
        mapping.put("JGYJ", "price_basis");
        mapping.put("ZS1", "note");
        mapping.put("HJBBS", "pricing_table_id");
        mapping.put("XTCJSJ", "sys_create_time");
        mapping.put("JGJC", "org_short_name");
        mapping.put("WPBS", "goods_id");
        mapping.put("LARQ", "register_date");
        mapping.put("FWZXSJGXSJ", "sysupdatetime");
        mapping.put("LSJJE", "retail_amount");
        mapping.put("XGSJ", "modify_time");
        mapping.put("XH", "serial_no");
        mapping.put("TTXM", "barcode");
        mapping.put("HJBMXBS", "pricing_detail_id");
        mapping.put("JBR", "handler");
        mapping.put("BZ", "remark");
        mapping.put("LABH", "register_no");
        mapping.put("XGR", "modifier");
        mapping.put("CJR", "creator");
        mapping.put("JE", "amount");
        mapping.put("SFYX", "is_active");
        mapping.put("WPDJDJBS", "goods_register_id");
        mapping.put("KZZD3", "ext3");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type == 1) {
            Map<String, Object> query = new HashMap<>();
            // query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getTobaccoPriceChangeApprovalDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });
                    return processData;
                }
            }
        }

        // 基础信息
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_name", "惠州市烟草专卖局");
        mockData.put("org_short_name", "惠州烟草");
        mockData.put("city_org_name", "惠州市");
        mockData.put("doc_date", "2025/6/10");
        mockData.put("register_date", "2025/3/18");
        mockData.put("register_no", "HZ2025001");

        // 当事人信息
        mockData.put("party", "梁俊强");

        // 物品信息
        mockData.put("goods_id", "goods_001");
        mockData.put("goods_detail_id", "item_detail_001");
        mockData.put("variety", "黄果树(长征)");
        mockData.put("unit", "条");
        mockData.put("quantity", 1075);
        mockData.put("total_quantity", 1075);
        mockData.put("wholesale_price", 101.05);
        mockData.put("retail_price", 101.05);
        mockData.put("amount", 108625.00);
        mockData.put("total_amount", 108625.00);
        mockData.put("retail_amount", 108625.00);
        mockData.put("goods_category", 1);
        mockData.put("barcode", "6901028123456");
        mockData.put("sample_loss_quantity", 0);

        // 价格相关
        mockData.put("pricing_table_id", "pricing_table_001");
        mockData.put("pricing_detail_id", "pricing_detail_001");
        mockData.put("price_basis", "广东省烟草专卖局2024年度价格标准");
        mockData.put("note", "涉案卷烟价格按照省局统一标准执行");

        // 处理信息
        mockData.put("handler", "蔡秋宝");
        mockData.put("handler_uuids", "4413231030000010001,4413231030000010002");
        mockData.put("complete_status", 1);
        mockData.put("serial_no", 1);
        mockData.put("remark", "变价处理审批表");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("sysisdelete", 0);
        mockData.put("sysupdatetime", "2025-06-10 17:15:00");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("sys_create_time", "2025/6/10 17:15");
        mockData.put("sys_modify_time", "2025/6/10 17:15");
        mockData.put("city_org_code", "10441300");
        mockData.put("mc_tec_ctime", "2025/6/11 1:03");
        mockData.put("goods_register_id", "goods_register_001");

        // 扩展字段
        mockData.put("tid", "tobacco_price_change_001");
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");

        return mockData;
    }
}
