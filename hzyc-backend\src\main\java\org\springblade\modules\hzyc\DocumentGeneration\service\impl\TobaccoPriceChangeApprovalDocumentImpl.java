package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 烟草专卖品变价处理审批表文档生成实现类
 *
 * <AUTHOR>
 */
@Service("tobaccoPriceChangeApprovalDocument")
public class TobaccoPriceChangeApprovalDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = StrUtil.isNotBlank(caseId) ? getData(dataType, caseId) : getMockData();
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "51烟草专卖品变价处理审批表.docx";
    }

    @Override
    public String getDocumentType() {
        return "TOBACCO-PRICE-CHANGE-APPROVAL";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("FWZXSJSCBJ", "sysisdelete");
        mapping.put("XYWYBS", "tid");
        mapping.put("ZZMC", "org_name");
        mapping.put("ZJE", "total_amount");
        mapping.put("WCZT", "complete_status");
        mapping.put("SJSSBM", "own_dept_uuid");
        mapping.put("KZZD1", "ext1");
        mapping.put("DW", "unit");
        mapping.put("AJBS", "case_uuid");
        mapping.put("WPFL", "goods_category");
        mapping.put("XTGXSJ", "sys_modify_time");
        mapping.put("CJSJ", "create_time");
        mapping.put("DSR", "party");
        mapping.put("ZS", "total_quantity");
        mapping.put("SJBM", "city_org_code");
        mapping.put("SJMC", "city_org_name");
        mapping.put("PFDJ", "wholesale_price");
        mapping.put("JBRUUIDS", "handler_uuids");
        mapping.put("MCRKSJ", "mc_tec_ctime");
        mapping.put("LYSHSL", "sample_loss_quantity");
        mapping.put("LSDJ", "retail_price");
        mapping.put("SL", "quantity");
        mapping.put("KZZD2", "ext2");
        mapping.put("SJSSDW", "own_org_uuid");
        mapping.put("PZ", "variety");
        mapping.put("WSRQ", "doc_date");
        mapping.put("WPMXBS", "goods_detail_id");
        mapping.put("JGYJ", "price_basis");
        mapping.put("ZS1", "note");
        mapping.put("HJBBS", "pricing_table_id");
        mapping.put("XTCJSJ", "sys_create_time");
        mapping.put("JGJC", "org_short_name");
        mapping.put("WPBS", "goods_id");
        mapping.put("LARQ", "register_date");
        mapping.put("FWZXSJGXSJ", "sysupdatetime");
        mapping.put("LSJJE", "retail_amount");
        mapping.put("XGSJ", "modify_time");
        mapping.put("XH", "serial_no");
        mapping.put("TTXM", "barcode");
        mapping.put("HJBMXBS", "pricing_detail_id");
        mapping.put("JBR", "handler");
        mapping.put("BZ", "remark");
        mapping.put("LABH", "register_no");
        mapping.put("XGR", "modifier");
        mapping.put("CJR", "creator");
        mapping.put("JE", "amount");
        mapping.put("SFYX", "is_active");
        mapping.put("WPDJDJBS", "goods_register_id");
        mapping.put("KZZD3", "ext3");

        return mapping;
    }

    /**
     * 获取数据（支持真实数据和模拟数据）
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getData(int type, String caseId) {
        Map<String, Object> data = new HashMap<>();

        if(type == 1) {
            // 正式环境：使用真实接口数据
            Map<String, Object> query = new HashMap<>();
            query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getTobaccoPriceChangeApprovalDailyReport(query);

            if(array != null && !array.isEmpty()) {
                Map<String, String> mapper = getReverseFieldMapping();

                // 处理第一条记录的基础信息
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                if(firstData != null) {
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        // 跳过变价处理相关字段，这些将在priceChangeList中处理
                        if (!isPriceChangeField(key)) {
                            // 处理时间戳格式化
                            if (value instanceof Number && (newKey.contains("date") || newKey.contains("time"))) {
                                try {
                                    long timestamp = ((Number) value).longValue();
                                    java.util.Date date = new java.util.Date(timestamp);
                                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日");
                                    data.put(newKey, sdf.format(date));

                                    // 添加详细的时间字段用于模板
                                    java.util.Calendar cal = java.util.Calendar.getInstance();
                                    cal.setTime(date);
                                    if ("doc_date".equals(newKey)) {
                                        data.put("doc_date_year", cal.get(java.util.Calendar.YEAR));
                                        data.put("doc_date_month", cal.get(java.util.Calendar.MONTH) + 1);
                                        data.put("doc_date_day", cal.get(java.util.Calendar.DAY_OF_MONTH));
                                    }
                                } catch (Exception e) {
                                    data.put(newKey, value);
                                }
                            } else {
                                data.put(newKey, value);
                            }
                        }
                    });
                }

                // 处理变价处理列表数据
                List<Map<String, Object>> priceChangeList = new ArrayList<>();
                for (int i = 0; i < array.size(); i++) {
                    Map<String, Object> item = (Map<String, Object>) array.get(i);
                    Map<String, Object> priceChangeItem = new HashMap<>();

                    // 映射变价处理相关字段，确保所有值都不为null
                    priceChangeItem.put("variety", item.get("PZ") != null ? item.get("PZ").toString() : ""); // 品种规格
                    priceChangeItem.put("quantity", item.get("SL") != null ? String.format("%.2f", Double.parseDouble(item.get("SL").toString())) : ""); // 数量
                    priceChangeItem.put("handle_price", item.get("LSDJ") != null ? String.format("%.2f", Double.parseDouble(item.get("LSDJ").toString())) : ""); // 处理单价
                    priceChangeItem.put("handle_amount", item.get("JE") != null ? String.format("%.2f", Double.parseDouble(item.get("JE").toString())) : ""); // 处理金额
                    priceChangeItem.put("unit", item.get("DW") != null ? item.get("DW").toString() : ""); // 单位
                    priceChangeItem.put("serial_no", item.get("XH") != null ? item.get("XH").toString() : ""); // 序号

                    priceChangeList.add(priceChangeItem);
                }

                data.put("priceChangeList", priceChangeList);

                // 同时为兼容性添加前几个物品的字段（用于简单模板）
                if (!priceChangeList.isEmpty()) {
                    Map<String, Object> firstItem = priceChangeList.get(0);
                    data.put("variety", firstItem.get("variety"));
                    data.put("quantity", firstItem.get("quantity"));
                    data.put("handle_price", firstItem.get("handle_price"));
                    data.put("handle_amount", firstItem.get("handle_amount"));
                    data.put("unit", firstItem.get("unit"));
                }

                return data;
            }
        }

        // 如果没有数据或使用模拟数据，返回模拟数据
        return getMockData();
    }

    /**
     * 判断是否为变价处理相关字段
     */
    private boolean isPriceChangeField(String key) {
        return "PZ".equals(key) || "SL".equals(key) || "LSDJ".equals(key) ||
               "JE".equals(key) || "DW".equals(key) || "XH".equals(key);
    }

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {

        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_name", "惠州市烟草专卖局");
        mockData.put("org_short_name", "惠州烟草");
        mockData.put("city_org_name", "惠州市");
        mockData.put("doc_date", "2025年6月10日");
        mockData.put("register_date", "2025年3月18日");
        mockData.put("register_no", "HZ2025001");
        mockData.put("case_name", "梁俊强涉嫌违法经营卷烟案");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");

        // 当事人信息
        mockData.put("party", "梁俊强");

        // 变价处理原因
        mockData.put("price_change_reason", "根据《广东省烟草专卖局涉案烟草制品价格管理规定》，对涉案违法物品进行价格重新评估和处理");

        // 变价处理物品清单
        List<Map<String, Object>> priceChangeList = new ArrayList<>();

        Map<String, Object> item1 = new HashMap<>();
        item1.put("variety", "黄果树(长征)");
        item1.put("quantity", "200.00");
        item1.put("handle_price", "101.05");
        item1.put("handle_amount", "20210.00");
        item1.put("unit", "条");
        item1.put("serial_no", "1");
        priceChangeList.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("variety", "白沙(硬精品三代)");
        item2.put("quantity", "150.00");
        item2.put("handle_price", "120.50");
        item2.put("handle_amount", "18075.00");
        item2.put("unit", "条");
        item2.put("serial_no", "2");
        priceChangeList.add(item2);

        Map<String, Object> item3 = new HashMap<>();
        item3.put("variety", "红塔山(硬经典)");
        item3.put("quantity", "150.00");
        item3.put("handle_price", "80.00");
        item3.put("handle_amount", "12000.00");
        item3.put("unit", "条");
        item3.put("serial_no", "3");
        priceChangeList.add(item3);

        mockData.put("priceChangeList", priceChangeList);

        // 兼容性字段（第一个物品的信息）
        if (!priceChangeList.isEmpty()) {
            Map<String, Object> firstItem = priceChangeList.get(0);
            mockData.put("variety", firstItem.get("variety"));
            mockData.put("quantity", firstItem.get("quantity"));
            mockData.put("handle_price", firstItem.get("handle_price"));
            mockData.put("handle_amount", firstItem.get("handle_amount"));
            mockData.put("unit", firstItem.get("unit"));
        }

        // 审批意见和签名
        mockData.put("dept_opinion", "经核查，申请变价处理的物品符合相关规定，同意按照省局统一价格标准进行处理。");
        mockData.put("dept_handler", "张三");
        mockData.put("dept_date_year", "2025");
        mockData.put("dept_date_month", "6");
        mockData.put("dept_date_day", "10");

        mockData.put("pricing_group_opinion", "经定价小组讨论，同意按照广东省烟草专卖局2024年度价格标准执行变价处理。");
        mockData.put("pricing_group_handler", "李四");
        mockData.put("pricing_date_year", "2025");
        mockData.put("pricing_date_month", "6");
        mockData.put("pricing_date_day", "11");

        mockData.put("leader_approval", "同意变价处理方案，按照相关规定执行。");
        mockData.put("leader_handler", "王五");
        mockData.put("leader_date_year", "2025");
        mockData.put("leader_date_month", "6");
        mockData.put("leader_date_day", "12");

        // 处理信息
        mockData.put("handler", "蔡秋宝");
        mockData.put("remark", "1. 变价处理严格按照省局统一标准执行；2. 处理结果已报省局备案；3. 相关档案材料已归档保存。");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("sysisdelete", 0);
        mockData.put("sysupdatetime", "2025-06-10 17:15:00");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("sys_create_time", "2025/6/10 17:15");
        mockData.put("sys_modify_time", "2025/6/10 17:15");
        mockData.put("city_org_code", "10441300");
        mockData.put("mc_tec_ctime", "2025/6/11 1:03");
        mockData.put("goods_register_id", "goods_register_001");

        // 扩展字段
        mockData.put("tid", "tobacco_price_change_001");
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");

        return mockData;
    }
}
