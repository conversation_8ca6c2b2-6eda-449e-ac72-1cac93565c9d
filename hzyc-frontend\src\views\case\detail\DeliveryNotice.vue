<template>
  <div class="penalty-decision-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>送达公告</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_shortname"
                placeholder="机构简称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>送达公告</h2>
            </div>

            <div class="document-number">
              <el-input
                v-model="formData.full_doc_no"
                placeholder="文号前缀"
                style="width: 200px;"
              />
              （
              <el-input
                v-model="formData.document_year"
                placeholder="年份"
                style="width: 80px;"
              />
              ）第
              <el-input
                v-model="formData.document_number"
                placeholder="编号"
                style="width: 80px;"
              />
              号
            </div>
          </div>

          <!-- 收件人信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.recipient_info"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="收件人信息"
                class="recipient-info auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
              ：
            </div>
          </div>

          <!-- 案件日期和类型 -->
          <div class="content-section case-section">
            <div class="case-date-line">
              <el-input
                v-model="formData.case_year"
                placeholder="年"
                style="width: 80px;"
              />
              年
              <el-input
                v-model="formData.case_month"
                placeholder="月"
                style="width: 60px;"
              />
              月
              <el-input
                v-model="formData.case_day"
                placeholder="日"
                style="width: 60px;"
              />
              日本局在
              <el-input
                v-model="formData.case_location"
                placeholder="地点"
                style="width: 150px;"
              />
              查获
            </div>
          </div>

          <!-- 案件详情 -->
          <div class="content-section">
            <div class="case-detail-line">
              一案，本局依据
              <el-input
                v-model="formData.legal_basis"
                placeholder="法律依据"
                style="width: 300px;"
              />
              的规定作出
              <el-input
                v-model="formData.decision_type"
                placeholder="决定类型"
                style="width: 150px;"
              />
            </div>
          </div>

          <!-- 决定内容 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.decision_content"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="决定内容"
                class="decision-content auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
              决定。根据《烟草专卖行政处罚程序规定》
            </div>
          </div>

          <!-- 法条引用 -->
          <div class="content-section">
            <div class="legal-reference-line">
              第五十五条第一款第四项的规定，现将
              <el-input
                v-model="formData.notice_content"
                placeholder="公告内容"
                style="width: 200px;"
              />
              予以公告送达。公告送达期限为30日，期限届满视为送达。
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>落款（印章）</span>
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.sys_modify_time"
                placeholder="日期"
                style="width: 200px;"
              />
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  full_doc_no: '',
  recipient_info: '',
  case_year: '',
  case_month: '',
  case_day: '',
  case_location: '',
  legal_basis: '',
  decision_type: '',
  decision_content: '',
  notice_content: '',
  org_shortname: '',
  document_year: '',
  document_number: '',
  sys_modify_time: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      recipient_info: docContent.recipient_info || docContent.recipientInfo || newVal.recipient_info || '',
      case_year: docContent.case_year || docContent.caseYear || newVal.case_year || '',
      case_month: docContent.case_month || docContent.caseMonth || newVal.case_month || '',
      case_day: docContent.case_day || docContent.caseDay || newVal.case_day || '',
      case_location: docContent.case_location || docContent.caseLocation || newVal.case_location || '',
      legal_basis: docContent.legal_basis || docContent.legalBasis || newVal.legal_basis || '',
      decision_type: docContent.decision_type || docContent.decisionType || newVal.decision_type || '',
      decision_content: docContent.decision_content || docContent.decisionContent || newVal.decision_content || '',
      notice_content: docContent.notice_content || docContent.noticeContent || newVal.notice_content || '',
      org_shortname: docContent.org_shortname || docContent.orgShortname || newVal.org_shortname || '',
      document_year: docContent.document_year || docContent.documentYear || newVal.document_year || '',
      document_number: docContent.document_number || docContent.documentNumber || newVal.document_number || '',
      sys_modify_time: docContent.sys_modify_time || docContent.sysModifyTime || newVal.sys_modify_time || '',
      full_doc_no: docContent.full_doc_no || docContent.fullDocNo || newVal.full_doc_no || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    recipient_info: formData.value.recipient_info,
    case_year: formData.value.case_year,
    case_month: formData.value.case_month,
    case_day: formData.value.case_day,
    case_location: formData.value.case_location,
    legal_basis: formData.value.legal_basis,
    decision_type: formData.value.decision_type,
    decision_content: formData.value.decision_content,
    notice_content: formData.value.notice_content,
    org_shortname: formData.value.org_shortname,
    document_year: formData.value.document_year,
    document_number: formData.value.document_number,
    sys_modify_time: formData.value.sys_modify_time,
    full_doc_no: formData.value.full_doc_no
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

/* 送达公告特有的样式 */
.case-date-line {
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.case-detail-line {
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.legal-reference-line {
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.recipient-info {
  margin-bottom: 10px;
}

.decision-content {
  margin-bottom: 10px;
}
</style>