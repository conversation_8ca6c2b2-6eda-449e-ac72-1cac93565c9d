<template>
  <div class="correction-notice-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>责令改正通知书</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.tobacco_bureau_name"
                placeholder="烟草专卖局名称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>责令改正通知书</h2>
            </div>

            <div class="document-number">
              <el-input
                v-model="formData.city_prefix"
                placeholder="地市简称"
                style="width: 80px;"
              />
              烟责〔
              <el-input
                v-model="formData.doc_year"
                placeholder="年份"
                style="width: 80px;"
              />
              〕第
              <el-input
                v-model="formData.doc_serial_number"
                placeholder="编号"
                style="width: 80px;"
              />
              号
            </div>
          </div>

          <!-- 当事人信息 -->
          <div class="content-section">
            <div class="recipient-section">
              <el-input
                v-model="formData.party_name"
                placeholder="当事人姓名"
                class="party-name-input"
                style="width: 200px;"
              />
              ：
            </div>
          </div>

          <!-- 违法行为描述 -->
          <div class="content-section">
            <div class="violation-section">
              <span>你（单位）</span>
              <el-input
                v-model="formData.illegal_behavior"
                placeholder="违法行为描述"
                class="behavior-input"
                style="width: 300px;"
              />
              <span>的行为，违反了</span>
              <el-input
                v-model="formData.violated_regulation"
                placeholder="违反的法规条款"
                class="regulation-input"
                style="width: 400px;"
              />
              <span>的规定，</span>
            </div>
          </div>

          <!-- 法律依据 -->
          <div class="content-section">
            <div class="legal-basis">
              <span>根据《中华人民共和国行政处罚法》第二十八条第一款的规定，</span>
            </div>
          </div>

          <!-- 责令改正内容 -->
          <div class="content-section">
            <div class="correction-section">
              <span>现责令你（单位）即时（或者于</span>
              <el-input
                v-model="formData.correction_year"
                placeholder="年"
                style="width: 60px;"
              />
              <span>年</span>
              <el-input
                v-model="formData.correction_month"
                placeholder="月"
                style="width: 50px;"
              />
              <span>月</span>
              <el-input
                v-model="formData.correction_day"
                placeholder="日"
                style="width: 50px;"
              />
              <span>日前）改正上述违法行为。我局将对你（单位）的改正情况进行复查。</span>
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>落款（印章）</span>
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.doc_year_date"
                placeholder="年"
                style="width: 60px;"
              />
              <span>年</span>
              <el-input
                v-model="formData.doc_month_date"
                placeholder="月"
                style="width: 50px;"
              />
              <span>月</span>
              <el-input
                v-model="formData.doc_day_date"
                placeholder="日"
                style="width: 50px;"
              />
              <span>日</span>
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  tobacco_bureau_name: '',
  city_prefix: '',
  doc_year: '',
  doc_serial_number: '',
  party_name: '',
  illegal_behavior: '',
  violated_regulation: '',
  correction_year: '',
  correction_month: '',
  correction_day: '',
  doc_year_date: '',
  doc_month_date: '',
  doc_day_date: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      tobacco_bureau_name: docContent.tobacco_bureau_name || newVal.tobacco_bureau_name || '',
      city_prefix: docContent.city_prefix || newVal.city_prefix || '',
      doc_year: docContent.doc_year || newVal.doc_year || '',
      doc_serial_number: docContent.doc_serial_number || newVal.doc_serial_number || '',
      party_name: docContent.party_name || newVal.party_name || '',
      illegal_behavior: docContent.illegal_behavior || newVal.illegal_behavior || '',
      violated_regulation: docContent.violated_regulation || newVal.violated_regulation || '',
      correction_year: docContent.correction_year || newVal.correction_year || '',
      correction_month: docContent.correction_month || newVal.correction_month || '',
      correction_day: docContent.correction_day || newVal.correction_day || '',
      doc_year_date: docContent.doc_year_date || newVal.doc_year_date || '',
      doc_month_date: docContent.doc_month_date || newVal.doc_month_date || '',
      doc_day_date: docContent.doc_day_date || newVal.doc_day_date || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    tobacco_bureau_name: formData.value.tobacco_bureau_name,
    city_prefix: formData.value.city_prefix,
    doc_year: formData.value.doc_year,
    doc_serial_number: formData.value.doc_serial_number,
    party_name: formData.value.party_name,
    illegal_behavior: formData.value.illegal_behavior,
    violated_regulation: formData.value.violated_regulation,
    correction_year: formData.value.correction_year,
    correction_month: formData.value.correction_month,
    correction_day: formData.value.correction_day,
    doc_year_date: formData.value.doc_year_date,
    doc_month_date: formData.value.doc_month_date,
    doc_day_date: formData.value.doc_day_date
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '责令改正通知书'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'correction') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

/* 责令改正通知书特有样式 */
.correction-notice-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recipient-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.violation-section {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.legal-basis {
  margin-bottom: 20px;
  line-height: 1.8;
}

.correction-section {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.party-name-input {
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
}

.behavior-input {
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
}

.regulation-input {
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
}

/* 编辑模式下的输入框样式 */
.document-layout .el-input {
  margin: 0 5px;
}

.document-layout .el-input .el-input__wrapper {
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #dcdfe6;
  box-shadow: none;
  padding: 2px 5px;
}

.document-layout .el-input .el-input__wrapper:hover {
  border-bottom: 1px solid #409eff;
}

.document-layout .el-input.is-focus .el-input__wrapper {
  border-bottom: 2px solid #409eff;
}

/* 签名区域样式 */
.signature-section {
  margin-top: 60px;
  text-align: right;
}

.signature-line {
  margin-bottom: 30px;
  font-size: 16px;
}

.date-line {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  font-size: 16px;
}

/* 文档头部区域样式 */
.document-header-section {
  text-align: center;
  margin-bottom: 40px;
}

.org-name {
  margin-bottom: 20px;
}

.document-title h2 {
  font-size: 24px;
  font-weight: bold;
  margin: 20px 0;
}

.document-number {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-size: 16px;
  margin-top: 20px;
}

/* 内容区域样式 */
.content-section {
  margin-bottom: 25px;
  line-height: 2;
  font-size: 16px;
}
</style>
