package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 责令改正通知书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("correctionNoticeDocument")
public class CorrectionNoticeDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "47责令改正通知书.docx";
    }

    @Override
    public String getDocumentType() {
        return "CORRECTION-NOTICE";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("TZRQ", "notice_date");
        mapping.put("SRRQ", "input_date");
        mapping.put("SJBM", "city_org_code");
        mapping.put("DSR", "party_name");
        mapping.put("JYDZ", "business_address");
        mapping.put("KZZD1", "ext1");
        mapping.put("JBR", "handler");
        mapping.put("AJUUID", "case_uuid");
        mapping.put("ZLJZRQ", "correction_deadline");
        mapping.put("XTGXSJCXBYDX", "sys_update_time_readonly");
        mapping.put("SJMC", "city_org_name");
        mapping.put("TZR", "notifier");
        mapping.put("LXR", "contact_person");
        mapping.put("AJH", "case_number");
        mapping.put("DWJC", "org_shortname");
        mapping.put("JBRUUIDS", "handler_uuids");
        mapping.put("XTSCBS", "sys_delete_flag");
        mapping.put("ZMJJC", "tobacco_bureau_shortname");
        mapping.put("SJSSDWYYTYSJQXCL", "data_owner_org_unified_permission");
        mapping.put("LXDH", "contact_phone");
        mapping.put("XGSJ", "modify_time");
        mapping.put("WFTK", "legal_clause");
        mapping.put("MCRKSJ", "mc_storage_time");
        mapping.put("XYWYBS", "industry_unique_id");
        mapping.put("CJR", "creator");
        mapping.put("WCZT", "completion_status");
        mapping.put("AJXZ", "case_nature");
        mapping.put("KZZD2", "ext2");
        mapping.put("KZZD3", "ext3");
        mapping.put("JCJG", "inspection_result");
        mapping.put("NF", "year");
        mapping.put("XTGXSJ", "sys_update_time");
        mapping.put("SJSSBMYYTYSJQXCL", "data_owner_dept_unified_permission");
        mapping.put("WSRQ", "doc_date");
        mapping.put("XGR", "modifier");
        mapping.put("ZJUUID", "primary_key_uuid");
        mapping.put("SFGZ", "is_corrected");
        mapping.put("XTCJSJCXBYDX", "sys_create_time_readonly");
        mapping.put("LXDZ", "contact_address");
        mapping.put("JCRQ", "inspection_date");
        mapping.put("CJSJ", "create_time");
        mapping.put("WSHQ", "full_doc_number");
        mapping.put("XKZH", "license_number");
        mapping.put("DWJC", "org_shortname"); // 可以从机构简称中提取地市简称

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type == 1){
            Map<String, Object> query = new HashMap<>();
            query.put("AJUUID", caseId);
            JSONArray array = icaseInfoService.getCorrectionNoticeDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });
                    return processData;
                }
            }
        }

        // 基础信息
        mockData.put("primary_key_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_number", "博烟责改﹝2025﹞第48号");
        mockData.put("doc_date", "2025/6/10");
        mockData.put("notice_date", "2025/6/10");
        mockData.put("input_date", "2025-06-10");

        // 模板字段 - 烟草专卖局名称
        mockData.put("tobacco_bureau_name", "广东省博罗县烟草专卖局");

        // 模板字段 - 文书编号拆分
        mockData.put("doc_year", "2025");
        mockData.put("doc_serial_number", "48");
        mockData.put("city_prefix", "博"); // 地市简称，如：博、惠、龙等

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("business_address", "广东省博罗县龙溪街道宫庭村龙桥大道1239号");
        mockData.put("contact_person", "梁俊强");
        mockData.put("contact_phone", "***********");
        mockData.put("contact_address", "广东省博罗县龙溪街道长湖村合湖小组193号");
        mockData.put("license_number", "441322113031");

        // 案件信息
        mockData.put("case_number", "博烟案﹝2025﹞第48号");
        mockData.put("case_nature", "未在当地烟草专卖批发企业进货");
        mockData.put("inspection_date", "2025-03-18");
        mockData.put("inspection_result", "发现涉嫌违法的烟草专卖品黄果树(长征)200条、白沙(硬精品三代)150条等17个品种1075条");

        // 违法条款
        mockData.put("legal_clause", "《中华人民共和国烟草专卖法实施条例》第二十三条第二款");

        // 模板字段 - 违法行为描述
        mockData.put("illegal_behavior", "未在当地烟草专卖批发企业进货");

        // 模板字段 - 违反的法规条款
        mockData.put("violated_regulation", "《中华人民共和国烟草专卖法实施条例》第二十三条第二款");

        // 责令改正信息
        mockData.put("correction_deadline", "2025/6/25");
        mockData.put("is_corrected", "否");

        // 模板字段 - 改正期限拆分
        mockData.put("correction_year", "2025");
        mockData.put("correction_month", "6");
        mockData.put("correction_day", "25");

        // 模板字段 - 文书日期拆分
        mockData.put("doc_year_date", "2025");
        mockData.put("doc_month_date", "6");
        mockData.put("doc_day_date", "10");

        // 经办人信息
        mockData.put("handler", "叶辉明,朱兆强");
        mockData.put("handler_uuids", "19090352015,19090352023");
        mockData.put("notifier", "叶辉明");

        // 机构信息
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");
        mockData.put("tobacco_bureau_shortname", "博罗烟草");

        // 系统字段
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("sys_create_time_readonly", "2025/6/10 17:15");
        mockData.put("sys_update_time_readonly", "2025/6/10 17:15");
        mockData.put("sys_update_time", "2025-06-10 17:15:00");
        mockData.put("mc_storage_time", "2025/6/11 1:03");
        mockData.put("sys_delete_flag", 0);
        mockData.put("completion_status", 1);
        mockData.put("year", 2025);

        // 数据权限字段
        mockData.put("data_owner_org_unified_permission", "4413231030000000540");
        mockData.put("data_owner_dept_unified_permission", "4413231030000002829");

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");
        mockData.put("industry_unique_id", "");

        return mockData;
    }
}
